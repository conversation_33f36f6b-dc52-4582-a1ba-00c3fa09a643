import httpx
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, Field

# --- <PERSON><PERSON><PERSON> hình <PERSON>ng dụng FastAPI ---
app = FastAPI(
    title="Davinci Token Refresh API",
    description="API để làm mới Firebase token, mô phỏng lại yêu cầu từ trình duyệt.",
    version="1.0.0",
)

# --- <PERSON><PERSON><PERSON> hằng số và cấu hình cho request ---

# API Key lấy từ Request URL
FIREBASE_API_KEY = "AIzaSyACc5e0U4DUwjdve3X4Odyjb8CnCL37Qgs"
FIREBASE_REFRESH_URL = f"https://securetoken.googleapis.com/v1/token?key={FIREBASE_API_KEY}"

# Sao chép chính xác các headers từ trình duyệt để tăng độ tin cậy
REQUEST_HEADERS = {
    "Content-Type": "application/x-www-form-urlencoded",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
    "Origin": "https://davinci.ai",
    "Referer": "https://davinci.ai/",
    "X-Client-Data": "CK21yQEIl7bJAQimtskBCKmdygELvpHLAQiOSocsBClagzQELo/LOAQiS9s4B",
    "X-Client-Version": "Chrome/JsCore/10.8.1/FirebaseCore-web",
    "X-Firebase-Gmpid": "1:378221804375:web:32bf22971597e5ef92dc12",
}

# Refresh token mặc định bạn đã cung cấp
DEFAULT_REFRESH_TOKEN = "AMf-vByfHb_hEdFfVmtFCE0iHclghhDAFSY9emyLgUYyEfcSSRBJvTGohzR4_qSjpXxEaZ228a-x8kW8iDFQlfxcDpI-XBkQ8lug_ghbh6KtdRBrtWIlJdCuqMkAKZEs7j3XuDPa-wH3SsGHZn2MEpUK8vcFGbCeDX3s9cwCVw3lG6PVyknL-P1kXAFSqL7vp-ODEdLTiFICQ_H4fLWE4VhEPGaZ-LxC5A"


# --- Định nghĩa mô hình dữ liệu cho Request Body ---
# Sử dụng Pydantic giúp FastAPI tự động xác thực và tạo tài liệu
class TokenRefreshRequest(BaseModel):
    refresh_token: str = Field(
        default=DEFAULT_REFRESH_TOKEN,
        description="Refresh token để lấy access token mới.",
        example=DEFAULT_REFRESH_TOKEN
    )


# --- Lớp xử lý logic gọi API ---
# Tách riêng logic giúp code sạch sẽ và dễ bảo trì
class FirebaseTokenRefresher:
    @staticmethod
    async def get_new_token(refresh_token: str) -> dict:
        """
        Thực hiện gọi API đến Firebase để làm mới token.

        Args:
            refresh_token: Token dùng để làm mới.

        Returns:
            Một dictionary chứa dữ liệu token mới.

        Raises:
            HTTPException: Nếu có lỗi mạng hoặc lỗi từ Firebase.
        """
        form_data = {
            'grant_type': 'refresh_token',
            'refresh_token': refresh_token
        }

        # Sử dụng httpx để thực hiện yêu cầu bất đồng bộ
        async with httpx.AsyncClient() as client:
            try:
                response = await client.post(
                    url=FIREBASE_REFRESH_URL,
                    headers=REQUEST_HEADERS,
                    data=form_data
                )
                # Ném lỗi nếu status code là 4xx hoặc 5xx
                response.raise_for_status()
                return response.json()

            except httpx.HTTPStatusError as exc:
                # Bắt lỗi từ server Firebase (ví dụ: token hết hạn, sai token)
                # và trả về lỗi chi tiết cho client
                raise HTTPException(
                    status_code=exc.response.status_code,
                    detail={
                        "message": "Lỗi từ Firebase Token API.",
                        "firebase_error": exc.response.json()
                    }
                ) from exc
            except httpx.RequestError as exc:
                # Bắt lỗi kết nối mạng
                raise HTTPException(
                    status_code=503, # Service Unavailable
                    detail=f"Không thể kết nối đến dịch vụ Firebase: {exc}"
                ) from exc


# --- Định nghĩa API Endpoint ---
@app.post("/api/v1/refresh-token", tags=["Authentication"])
async def refresh_token_endpoint(request_body: TokenRefreshRequest):
    """
    Endpoint để làm mới một Firebase token.

    Bạn có thể gửi một JSON body chứa `refresh_token` của riêng bạn,
    hoặc không gửi gì để sử dụng token mặc định đã được cung cấp.
    """
    new_token_data = await FirebaseTokenRefresher.get_new_token(request_body.refresh_token)
    return {
        "message": "Làm mới token thành công!",
        "data": new_token_data
    }

@app.get("/", tags=["General"])
def read_root():
    return {"message": "Chào mừng đến với API làm mới Token. Hãy truy cập /docs để xem chi tiết."}